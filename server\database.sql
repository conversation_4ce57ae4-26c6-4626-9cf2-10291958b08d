/*
Navicat MySQL Data Transfer

Source Server         : localhost
Source Server Version : 50505
Source Host           : localhost:3306
Source Database       : satulisan

Target Server Type    : MYSQL
Target Server Version : 50505
File Encoding         : 65001

Date: 2025-07-25 15:11:21
*/

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for branches
-- ----------------------------
DROP TABLE IF EXISTS `branches`;
CREATE TABLE `branches` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `manager` varchar(100) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `email` varchar(100) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `user_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of branches
-- ----------------------------
INSERT INTO `branches` VALUES ('1', 'Cabang', 'Malang', '085885263097', 'Moch Arizal Fauzi', '1', '<EMAIL>', 'cabang', '2025-07-25 13:47:15', '2025-07-25 13:47:15', '1');

-- ----------------------------
-- Table structure for cashiers
-- ----------------------------
DROP TABLE IF EXISTS `cashiers`;
CREATE TABLE `cashiers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `branch_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of cashiers
-- ----------------------------
INSERT INTO `cashiers` VALUES ('1', 'Kasir Pusat', '<EMAIL>', 'kasir', null, '1', '1', '2025-07-25 13:46:29', '2025-07-25 13:46:29');

-- ----------------------------
-- Table structure for categories
-- ----------------------------
DROP TABLE IF EXISTS `categories`;
CREATE TABLE `categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `value` varchar(50) DEFAULT NULL,
  `label` varchar(100) DEFAULT NULL,
  `icon` varchar(50) DEFAULT NULL,
  `count` int(11) DEFAULT 0,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `user_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of categories
-- ----------------------------
INSERT INTO `categories` VALUES ('1', 'makanan', 'Makanan', '?️', '0', '2025-06-02 16:22:46', '2025-07-25 13:45:46', '1');
INSERT INTO `categories` VALUES ('2', 'minuman', 'Minuman', '?', '0', '2025-06-02 16:23:00', '2025-07-25 13:45:54', '1');
INSERT INTO `categories` VALUES ('3', 'snack', 'Snack', '?', '0', '2025-06-02 16:23:24', '2025-07-25 13:45:59', '1');
INSERT INTO `categories` VALUES ('5', 'dessert', 'Dessert', '?', '0', '2025-07-22 09:25:50', '2025-07-22 09:25:50', '1');
INSERT INTO `categories` VALUES ('6', 'vegetarian', 'Vegetarian', '?', '0', '2025-07-23 08:18:27', '2025-07-23 08:18:27', '1');
INSERT INTO `categories` VALUES ('7', 'breakfast', 'Breakfast', '?', '0', '2025-07-23 08:19:45', '2025-07-23 08:19:45', '1');
INSERT INTO `categories` VALUES ('8', 'makanan', 'Makanan', '/uploads/icons/icon-1753327610876-265874702.png', '0', '2025-07-24 09:53:59', '2025-07-24 10:26:51', '9');

-- ----------------------------
-- Table structure for expenses
-- ----------------------------
DROP TABLE IF EXISTS `expenses`;
CREATE TABLE `expenses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `description` varchar(255) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `category` varchar(100) DEFAULT NULL,
  `date` datetime NOT NULL,
  `branch_id` int(11) DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of expenses
-- ----------------------------
INSERT INTO `expenses` VALUES ('2', 'Pengeluaran Admin', '50000.00', 'Test', '2025-07-25 00:00:00', null, '1', '2025-07-25 14:28:02', '2025-07-25 14:28:09');

-- ----------------------------
-- Table structure for members
-- ----------------------------
DROP TABLE IF EXISTS `members`;
CREATE TABLE `members` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `birthdate` date DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `notes` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of members
-- ----------------------------

-- ----------------------------
-- Table structure for msusers
-- ----------------------------
DROP TABLE IF EXISTS `msusers`;
CREATE TABLE `msusers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `role` varchar(20) DEFAULT 'admin',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of msusers
-- ----------------------------
INSERT INTO `msusers` VALUES ('1', 'Satu Lisan', '<EMAIL>', 'admin', 'admin', '1', '2025-05-29 20:48:46', '2025-07-22 08:13:59');

-- ----------------------------
-- Table structure for payment_gateways
-- ----------------------------
DROP TABLE IF EXISTS `payment_gateways`;
CREATE TABLE `payment_gateways` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `provider` varchar(50) DEFAULT NULL,
  `is_production` tinyint(1) DEFAULT 0,
  `client_key` varchar(255) DEFAULT NULL,
  `server_key` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 0,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `user_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of payment_gateways
-- ----------------------------
INSERT INTO `payment_gateways` VALUES ('1', 'midtrans', '0', 'SB-Mid-client-qPWFjzplfWLiqwC_', 'SB-Mid-server-35Tw59xovs6dMDWb5_4gTXUb', '1', '2025-06-17 09:54:16', '2025-06-17 09:54:16', '1');

-- ----------------------------
-- Table structure for payment_gateway_transactions
-- ----------------------------
DROP TABLE IF EXISTS `payment_gateway_transactions`;
CREATE TABLE `payment_gateway_transactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_id` varchar(255) DEFAULT NULL,
  `gateway_transaction_id` varchar(255) DEFAULT NULL,
  `gateway_provider` varchar(50) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  `payment_type` varchar(50) DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  `response_data` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of payment_gateway_transactions
-- ----------------------------

-- ----------------------------
-- Table structure for payment_methods
-- ----------------------------
DROP TABLE IF EXISTS `payment_methods`;
CREATE TABLE `payment_methods` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) DEFAULT NULL,
  `type` enum('cash','bank_transfer','qris','card','e_wallet','other') DEFAULT 'other',
  `description` text DEFAULT NULL,
  `account_number` varchar(50) DEFAULT NULL,
  `account_name` varchar(100) DEFAULT NULL,
  `bank_name` varchar(100) DEFAULT NULL,
  `qris_image` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `wallet_number` varchar(255) DEFAULT NULL,
  `wallet_provider` varchar(255) DEFAULT NULL,
  `use_gateway` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Records of payment_methods
-- ----------------------------
INSERT INTO `payment_methods` VALUES ('1', 'QRIS', 'qris', '', null, null, null, null, '1', '2025-06-17 09:50:47', '2025-06-17 09:50:47', null, null, '1', '1');

-- ----------------------------
-- Table structure for products
-- ----------------------------
DROP TABLE IF EXISTS `products`;
CREATE TABLE `products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) DEFAULT NULL,
  `price` decimal(10,2) DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `category` varchar(50) DEFAULT NULL,
  `category_label` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `is_active` int(11) DEFAULT NULL,
  `cost_price` bigint(20) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `branch_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of products
-- ----------------------------
INSERT INTO `products` VALUES ('1', 'Nasi goreng', '25000.00', 'https://sanex.co.id/wp-content/uploads/2024/11/2734.jpg', 'makanan', 'Makanan', '2025-06-02 16:25:10', '2025-06-17 09:17:57', '1', '12000', '1', null);
INSERT INTO `products` VALUES ('2', 'Mie goreng', '25000.00', 'https://allofresh.id/blog/wp-content/uploads/2023/09/cara-membuat-mie-goreng-4-1-scaled.jpg', 'makanan', 'Makanan', '2025-06-02 16:26:10', '2025-06-17 09:18:12', '1', '10000', '1', null);
INSERT INTO `products` VALUES ('3', 'Juice Strawberry', '15000.00', 'https://brokebankvegan.com/wp-content/uploads/2022/08/Strawberry-Juice-10.jpg', 'minuman', 'Minuman', '2025-06-02 16:27:31', '2025-06-17 09:18:50', '1', '10000', '1', null);
INSERT INTO `products` VALUES ('4', 'Juice Melon', '15000.00', 'https://img-global.cpcdn.com/recipes/a2f74f991e52dfcf/680x482f0.5_0.520114_1.0q90/jus-melon-dan-strawberry-foto-resep-utama.jpg', 'minuman', 'Minuman', '2025-06-02 16:27:56', '2025-06-17 09:19:33', '1', '10000', '1', null);
INSERT INTO `products` VALUES ('5', 'Dessert Nastar Crumble ', '25000.00', '/uploads/product-*************-*********.jpg', 'dessert', 'Dessert', '2025-07-22 09:33:38', '2025-07-25 13:45:34', '0', '15000', '1', null);
INSERT INTO `products` VALUES ('6', 'Dessert Box Oreo', '30000.00', '/uploads/product-*************-*********.jpg', 'dessert', 'Dessert', '2025-07-22 09:35:58', '2025-07-25 13:45:35', '0', '20000', '1', null);
INSERT INTO `products` VALUES ('7', 'Kopi', '5000.00', '/uploads/product-*************-*********.jpg', 'minuman', 'Minuman', '2025-07-22 10:25:35', '2025-07-25 13:45:26', '0', '3000', '1', null);
INSERT INTO `products` VALUES ('8', 'Es Teh', '5000.00', '/uploads/product-1753154762193-340835452.jpg', 'minuman', 'Minuman', '2025-07-22 10:26:02', '2025-07-25 13:45:32', '0', '3000', '1', null);
INSERT INTO `products` VALUES ('9', 'Kentang Goreng', '30000.00', '/uploads/product-1753173940190-744944165.jpg', 'snack', 'Snack', '2025-07-22 11:05:00', '2025-07-25 13:45:28', '0', '15000', '1', null);
INSERT INTO `products` VALUES ('10', 'Jamur Krispy', '30000.00', '/uploads/product-1753157137763-392771300.jpg', 'snack', 'Snack', '2025-07-22 11:05:38', '2025-07-25 13:45:30', '0', '15000', '1', null);
INSERT INTO `products` VALUES ('11', 'w', '31232.00', 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBxMTEhUTExMWFhUXGB0YGRgXGBsYGxsgHRcXIB0dGBofHSggHRslHRgYITEhJSkrLi4uGh8zODMtNygtLisBCgoKDg0OGxAQGy0mICUvLS0vLS0tLS0vLS0tLS8tLS0tLS0tMi0tLSstLS0tLS8tLS0tLS0tLS0tLS0tLS0tLf/AABEIALABHgMBIgACEQEDEQH/', 'makanan', 'Makanan', '2025-07-22 13:18:44', '2025-07-22 13:18:47', '0', '0', '1', null);
INSERT INTO `products` VALUES ('12', 'Tahu Krispy', '10000.00', '/uploads/product-1753233438357-232891989.jpg', 'snack', 'Snack', '2025-07-23 08:15:00', '2025-07-25 13:45:23', '0', '5000', '1', null);
INSERT INTO `products` VALUES ('13', 'Kopi Susu', '10000.00', 'https://jogjavoice.com/wp-content/uploads/2025/04/kopi-susu-ultra-milk-800x500.jpeg', 'minuman', 'Minuman', '2025-07-23 08:19:21', '2025-07-23 08:19:21', '1', '5000', '1', null);
INSERT INTO `products` VALUES ('14', 'Toast', '29000.00', 'https://www.allrecipes.com/thmb/m7LSrMCa5TX0ZAO0lP1ias9phyo=/1500x0/filters:no_upscale():max_bytes(150000):strip_icc()/7016-french-toast-i-ddmfs-beauty-4x3-b77fcd549a18443da3cf488687eae64f.jpg', 'breakfast', 'Breakfast', '2025-07-23 08:21:24', '2025-07-23 08:21:24', '1', '10000', '1', null);
INSERT INTO `products` VALUES ('15', 'Sandwich', '30000.00', 'https://staticcookist.akamaized.net/wp-content/uploads/sites/22/2025/05/clubsandwich.jpg', 'breakfast', 'Breakfast', '2025-07-23 08:41:53', '2025-07-23 08:41:53', '1', '15000', '1', null);
INSERT INTO `products` VALUES ('16', 'Rendang', '10000.00', 'https://awsimages.detik.net.id/community/media/visual/2025/03/27/resep-rendang-asli-padang-1743042209951_43.jpeg?w=600&q=90', 'makanan', 'Makanan', '2025-07-24 09:54:35', '2025-07-24 09:54:35', '1', '7500', '1', '9');

-- ----------------------------
-- Table structure for store_config
-- ----------------------------
DROP TABLE IF EXISTS `store_config`;
CREATE TABLE `store_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `store_name` varchar(100) DEFAULT 'Bakery POS',
  `store_address` varchar(255) DEFAULT NULL,
  `store_phone` varchar(20) DEFAULT NULL,
  `store_email` varchar(100) DEFAULT NULL,
  `tax_percentage` decimal(5,2) DEFAULT 0.00,
  `currency` varchar(10) DEFAULT 'IDR',
  `logo` varchar(255) DEFAULT NULL,
  `receipt_footer` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `user_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of store_config
-- ----------------------------
INSERT INTO `store_config` VALUES ('1', 'Satu Lisan', null, null, null, '11.00', 'IDR', null, null, '2025-06-02 20:40:42', '2025-07-25 13:41:49', '1');

-- ----------------------------
-- Table structure for transactions
-- ----------------------------
DROP TABLE IF EXISTS `transactions`;
CREATE TABLE `transactions` (
  `id` varchar(50) NOT NULL,
  `date` datetime DEFAULT NULL,
  `customer_name` varchar(100) DEFAULT NULL,
  `branch_id` int(11) DEFAULT NULL,
  `subtotal` decimal(10,2) DEFAULT NULL,
  `tax` decimal(10,2) DEFAULT 0.00,
  `total` decimal(10,2) DEFAULT NULL,
  `amount_paid` decimal(10,2) DEFAULT NULL,
  `change_amount` decimal(10,2) DEFAULT NULL,
  `payment_method` varchar(50) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `payment_method_id` int(11) DEFAULT NULL,
  `member_id` int(11) DEFAULT NULL,
  `payment_status` enum('paid','cancelled','pending') DEFAULT NULL,
  `outlet_id` int(11) DEFAULT NULL,
  `cashier_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of transactions
-- ----------------------------
INSERT INTO `transactions` VALUES ('TRX-1753426001000', '2025-07-25 13:46:49', 'Customer', null, '149000.00', '16390.00', '165390.00', '165390.00', '0.00', 'Cash', '2025-07-25 13:46:49', null, null, 'paid', null, '1');

-- ----------------------------
-- Table structure for transaction_items
-- ----------------------------
DROP TABLE IF EXISTS `transaction_items`;
CREATE TABLE `transaction_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_id` varchar(50) DEFAULT NULL,
  `product_name` varchar(100) DEFAULT NULL,
  `price` decimal(10,2) DEFAULT NULL,
  `quantity` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `product_id` int(11) DEFAULT NULL,
  `cost_price` decimal(10,0) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of transaction_items
-- ----------------------------
INSERT INTO `transaction_items` VALUES ('1', 'TRX-1753426001000', 'Juice Melon', '15000.00', '1', '2025-07-25 13:46:49', '4', '10000');
INSERT INTO `transaction_items` VALUES ('2', 'TRX-1753426001000', 'Juice Strawberry', '15000.00', '1', '2025-07-25 13:46:49', '3', '10000');
INSERT INTO `transaction_items` VALUES ('3', 'TRX-1753426001000', 'Kopi Susu', '10000.00', '1', '2025-07-25 13:46:49', '13', '5000');
INSERT INTO `transaction_items` VALUES ('4', 'TRX-1753426001000', 'Mie goreng', '25000.00', '1', '2025-07-25 13:46:49', '2', '10000');
INSERT INTO `transaction_items` VALUES ('5', 'TRX-1753426001000', 'Nasi goreng', '25000.00', '1', '2025-07-25 13:46:49', '1', '12000');
INSERT INTO `transaction_items` VALUES ('6', 'TRX-1753426001000', 'Sandwich', '30000.00', '1', '2025-07-25 13:46:49', '15', '15000');
INSERT INTO `transaction_items` VALUES ('7', 'TRX-1753426001000', 'Toast', '29000.00', '1', '2025-07-25 13:46:49', '14', '10000');

-- ----------------------------
-- Function structure for roman_to_int
-- ----------------------------
DROP FUNCTION IF EXISTS `roman_to_int`;
DELIMITER ;;
CREATE DEFINER=`karpeld1`@`10.10.0.29` FUNCTION `roman_to_int`(`roman` VARCHAR(15)) RETURNS int(11)
    DETERMINISTIC
BEGIN
    DECLARE num INT DEFAULT 0;
    DECLARE len INT DEFAULT LENGTH(roman);
    DECLARE i INT DEFAULT 1;
    DECLARE digit CHAR(1);
    DECLARE value INT;
    DECLARE next_value INT;

    WHILE i <= len DO
        SET digit = SUBSTRING(roman, i, 1);
        SET value = 
            CASE digit
                WHEN 'I' THEN 1
                WHEN 'V' THEN 5
                WHEN 'X' THEN 10
                WHEN 'L' THEN 50
                WHEN 'C' THEN 100
                WHEN 'D' THEN 500
                WHEN 'M' THEN 1000
                ELSE 0
            END;

        IF i < len THEN
            SET next_value = 
                CASE SUBSTRING(roman, i + 1, 1)
                    WHEN 'I' THEN 1
                    WHEN 'V' THEN 5
                    WHEN 'X' THEN 10
                    WHEN 'L' THEN 50
                    WHEN 'C' THEN 100
                    WHEN 'D' THEN 500
                    WHEN 'M' THEN 1000
                    ELSE 0
                END;
            IF value < next_value THEN
                SET value = -value;
            END IF;
        END IF;

        SET num = num + value;
        SET i = i + 1;
    END WHILE;

    RETURN num;
END
;;
DELIMITER ;
