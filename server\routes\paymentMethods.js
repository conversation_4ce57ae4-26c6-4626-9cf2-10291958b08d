const express = require('express');
const router = express.Router();
const pool = require('../config/database');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const PaymentMethod = require('../models/PaymentMethod');

// Helper function to determine user role using user_type from session
async function getUserRole(user_id, user_type = null) {
  let userRole = null;

  // Use user_type from session to determine which table to check
  if (user_type === 'branch') {
    // Check branches table for branch users
    const [branchResult] = await pool.query('SELECT id FROM branches WHERE id = ? AND is_active = 1', [user_id]);
    if (branchResult.length > 0) {
      userRole = 'branch';
    }
  } else if (user_type === 'cashier') {
    // Check cashiers table for cashier users
    const [cashierResult] = await pool.query('SELECT id FROM cashiers WHERE id = ? AND is_active = 1', [user_id]);
    if (cashierResult.length > 0) {
      userRole = 'cashier';
    }
  } else if (user_type === 'msuser') {
    // Check msusers table for admin/other users
    const [userResult] = await pool.query('SELECT role FROM msusers WHERE id = ? AND is_active = 1', [user_id]);
    if (userResult.length > 0) {
      userRole = userResult[0].role;
    }
  } else {
    // Fallback: check all tables if user_type is not provided (backward compatibility)
    const [userResult] = await pool.query('SELECT role FROM msusers WHERE id = ? AND is_active = 1', [user_id]);
    if (userResult.length > 0) {
      userRole = userResult[0].role;
    } else {
      const [branchResult] = await pool.query('SELECT id FROM branches WHERE id = ? AND is_active = 1', [user_id]);
      if (branchResult.length > 0) {
        userRole = 'branch';
      } else {
        const [cashierResult] = await pool.query('SELECT id FROM cashiers WHERE id = ? AND is_active = 1', [user_id]);
        if (cashierResult.length > 0) {
          userRole = 'cashier';
        }
      }
    }
  }

  return userRole;
}

// Konfigurasi penyimpanan untuk upload gambar QRIS
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../uploads/qris');

    // Buat direktori jika belum ada
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'qris-' + uniqueSuffix + ext);
  }
});

const upload = multer({
  storage: storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // Limit 5MB
  fileFilter: function (req, file, cb) {
    // Hanya izinkan file gambar
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Hanya file gambar yang diizinkan'));
    }
  }
});

// Get all payment methods
router.get('/', async (req, res) => {
  try {
    const { user_id, cashier_id, user_type } = req.query;
    let { branch_id } = req.query;

    // Jika request dari kasir, tentukan branch_id
    if (cashier_id) {
      const [cashierResult] = await pool.query(
        'SELECT branch_id FROM cashiers WHERE id = ?',
        [cashier_id]
      );

      if (cashierResult.length === 0) {
        return res.status(404).json({ error: 'Cashier not found' });
      }

      branch_id = cashierResult[0].branch_id;
    }

    if (!user_id) {
      return res.status(400).json({ error: 'User ID diperlukan' });
    }

    let query = `SELECT * FROM ${PaymentMethod.$table}`;
    const params = [];

    // Jika request dari cabang (branch), ambil metode pembayaran dari admin (parent) dan cabang itu sendiri
    if (branch_id) {
      // Dapatkan user_id (admin/parent) dari cabang
      const [branchResult] = await pool.query(
        'SELECT user_id FROM branches WHERE id = ?',
        [branch_id]
      );

      if (branchResult.length > 0) {
        const adminId = branchResult[0].user_id;
        query += ' WHERE (user_id = ? OR user_id = ?) AND is_active = 1';
        params.push(adminId, user_id);
      } else {
        query += ' WHERE user_id = ? AND is_active = 1';
        params.push(user_id);
      }
    } else {
      // Jika request dari admin, hanya ambil metode pembayaran miliknya
      query += ' WHERE user_id = ? AND is_active = 1';
      params.push(user_id);
    }

    // Add sorting by name
    query += ' ORDER BY name ASC';

    const [rows] = await pool.query(query, params);
    res.json(rows);
  } catch (error) {
    console.error('Error fetching payment methods:', error);
    res.status(500).json({ error: 'Gagal mengambil data metode pembayaran' });
  }
});

// Get payment method by ID
router.get('/:id', async (req, res) => {
  try {
    const { user_id } = req.query;

    let query = `SELECT * FROM ${PaymentMethod.$table} WHERE id = ?`;
    const params = [req.params.id];

    // Filter berdasarkan user_id jika ada
    if (user_id) {
      query += ' AND user_id = ?';
      params.push(user_id);
    }

    const [rows] = await pool.query(query, params);

    if (rows.length === 0) {
      return res.status(404).json({ error: 'Metode pembayaran tidak ditemukan' });
    }

    const method = rows[0];

    // Tambahkan URL untuk gambar QRIS
    if (method.qris_image && method.type === 'qris') {
      method.qris_image_url = `${req.protocol}://${req.get('host')}/uploads/qris/${method.qris_image}`;
    }

    res.json(method);
  } catch (error) {
    console.error('Error getting payment method:', error);
    res.status(500).json({ error: 'Gagal mengambil data metode pembayaran' });
  }
});

// Create new payment method
router.post('/', upload.single('qris_image'), async (req, res) => {
  try {
    const {
      name,
      type,
      description,
      account_number,
      account_name,
      bank_name,
      wallet_provider,
      wallet_number,
      is_active,
      use_gateway,
      user_id
    } = req.body;

    // Validasi data
    if (!name || !type || !user_id) {
      return res.status(400).json({ error: 'Nama, tipe metode pembayaran, dan user_id harus diisi' });
    }

    // Validasi data berdasarkan tipe
    if (type === 'bank_transfer' && (!account_number || !account_name || !bank_name)) {
      return res.status(400).json({ error: 'Data rekening bank harus lengkap' });
    }

    if (type === 'qris' && !use_gateway && !req.file) {
      return res.status(400).json({ error: 'Gambar QRIS harus diunggah' });
    }

    if (type === 'e_wallet' && (!wallet_provider || !wallet_number)) {
      return res.status(400).json({ error: 'Data e-wallet harus lengkap' });
    }

    // Siapkan data untuk disimpan
    const data = {
      name,
      type,
      description: description || '',
      is_active: is_active === '1' ? 1 : 0,
      user_id
    };

    // Tambahkan data sesuai tipe
    if (type === 'bank_transfer') {
      data.account_number = account_number;
      data.account_name = account_name;
      data.bank_name = bank_name;
    } else if (type === 'qris') {
      data.use_gateway = use_gateway === '1' ? 1 : 0;
      if (req.file && !data.use_gateway) {
        data.qris_image = req.file.filename;
      }
    } else if (type === 'e_wallet') {
      data.wallet_provider = wallet_provider;
      data.wallet_number = wallet_number;
    }

    // Buat query dan parameter
    const fields = Object.keys(data).join(', ');
    const placeholders = Object.keys(data).map(() => '?').join(', ');
    const values = Object.values(data);

    const [result] = await pool.query(
      `INSERT INTO ${PaymentMethod.$table} (${fields}) VALUES (${placeholders})`,
      values
    );

    if (result.affectedRows === 0) {
      return res.status(500).json({ error: 'Gagal menambahkan metode pembayaran' });
    }

    // Tambahkan ID ke data
    data.id = result.insertId;

    // Tambahkan URL untuk gambar QRIS
    if (data.qris_image) {
      data.qris_image_url = `${req.protocol}://${req.get('host')}/uploads/qris/${data.qris_image}`;
    }

    res.status(201).json(data);
  } catch (error) {
    console.error('Error creating payment method:', error);
    res.status(500).json({ error: 'Gagal menambahkan metode pembayaran' });
  }
});

// Update payment method
router.put('/:id', upload.single('qris_image'), async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      type,
      description,
      account_number,
      account_name,
      bank_name,
      wallet_provider,
      wallet_number,
      is_active,
      use_gateway,
      user_id
    } = req.body;

    // Validasi data
    if (!name || !type || !user_id) {
      return res.status(400).json({ error: 'Nama, tipe metode pembayaran, dan user_id harus diisi' });
    }

    // Cek apakah metode pembayaran ada dan milik user yang sama
    const [existingRows] = await pool.query(
      `SELECT * FROM ${PaymentMethod.$table} WHERE id = ? AND user_id = ?`,
      [id, user_id]
    );

    if (existingRows.length === 0) {
      return res.status(404).json({ error: 'Metode pembayaran tidak ditemukan atau tidak berwenang' });
    }

    const existingMethod = existingRows[0];

    // Siapkan data untuk diupdate
    const data = {
      name,
      type,
      description: description || '',
      is_active: is_active === '1' ? 1 : 0
    };

    // Reset field yang tidak relevan dengan tipe yang baru
    if (type !== 'bank_transfer') {
      data.account_number = null;
      data.account_name = null;
      data.bank_name = null;
    }

    if (type !== 'qris') {
      data.qris_image = null;
      data.use_gateway = 0;
    }

    if (type !== 'e_wallet') {
      data.wallet_provider = null;
      data.wallet_number = null;
    }

    // Tambahkan data sesuai tipe
    if (type === 'bank_transfer') {
      data.account_number = account_number;
      data.account_name = account_name;
      data.bank_name = bank_name;
    } else if (type === 'qris') {
      data.use_gateway = use_gateway === '1' ? 1 : 0;

      // Jika ada file baru dan tidak menggunakan gateway, update gambar
      if (req.file && !data.use_gateway) {
        // Hapus file lama jika ada
        if (existingMethod.qris_image) {
          const oldFilePath = path.join(__dirname, '../uploads/qris', existingMethod.qris_image);
          if (fs.existsSync(oldFilePath)) {
            fs.unlinkSync(oldFilePath);
          }
        }

        data.qris_image = req.file.filename;
      } else if (!req.file && !data.use_gateway) {
        // Jika tidak ada file baru dan tidak menggunakan gateway, pertahankan gambar lama
        data.qris_image = existingMethod.qris_image;
      }
    } else if (type === 'e_wallet') {
      data.wallet_provider = wallet_provider;
      data.wallet_number = wallet_number;
    }

    // Buat query dan parameter
    const setClause = Object.keys(data).map(key => `${key} = ?`).join(', ');
    const values = [...Object.values(data), id];

    const [result] = await pool.query(
      `UPDATE ${PaymentMethod.$table} SET ${setClause} WHERE id = ?`,
      values
    );

    if (result.affectedRows === 0) {
      return res.status(500).json({ error: 'Gagal mengupdate metode pembayaran' });
    }

    // Tambahkan ID ke data
    data.id = parseInt(id);
    data.user_id = parseInt(user_id);

    // Tambahkan URL untuk gambar QRIS
    if (data.qris_image) {
      data.qris_image_url = `${req.protocol}://${req.get('host')}/uploads/qris/${data.qris_image}`;
    }

    res.json(data);
  } catch (error) {
    console.error('Error updating payment method:', error);
    res.status(500).json({ error: 'Gagal mengupdate metode pembayaran' });
  }
});

// Delete payment method
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { user_id } = req.query;

    if (!user_id) {
      return res.status(400).json({ error: 'User ID diperlukan' });
    }

    // Cek apakah metode pembayaran ada dan milik user yang sama
    const [existingRows] = await pool.query(
      `SELECT * FROM ${PaymentMethod.$table} WHERE id = ? AND user_id = ?`,
      [id, user_id]
    );

    if (existingRows.length === 0) {
      return res.status(404).json({ error: 'Metode pembayaran tidak ditemukan atau tidak berwenang' });
    }

    const existingMethod = existingRows[0];

    // Hapus gambar QRIS jika ada
    if (existingMethod.qris_image) {
      const oldFilePath = path.join(__dirname, '../uploads/qris', existingMethod.qris_image);
      if (fs.existsSync(oldFilePath)) {
        fs.unlinkSync(oldFilePath);
      }
    }

    // Hapus metode pembayaran
    const [result] = await pool.query(
      `DELETE FROM ${PaymentMethod.$table} WHERE id = ?`,
      [id]
    );

    if (result.affectedRows === 0) {
      return res.status(500).json({ error: 'Gagal menghapus metode pembayaran' });
    }

    res.json({ message: 'Metode pembayaran berhasil dihapus' });
  } catch (error) {
    console.error('Error deleting payment method:', error);
    res.status(500).json({ error: 'Gagal menghapus metode pembayaran' });
  }
});

module.exports = router;



