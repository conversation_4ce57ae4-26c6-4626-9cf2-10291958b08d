import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft, Plus, Edit, Trash2, Search } from 'lucide-react';
import { fetchWithSession, getCurrentUser } from '../utils/api';
import config from '../config';

interface Cashier {
  id: number;
  name: string;
  email: string;
  branch_id: number | null;
  branch_name: string | null;
  user_id: string;
  is_active: number;
}

interface Branch {
  id: number;
  name: string;
}

const CashierManagement: React.FC = () => {
  const [cashiers, setCashiers] = useState<Cashier[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [cashierToDelete, setCashierToDelete] = useState<Cashier | null>(null);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [selectedBranchId, setSelectedBranchId] = useState<string>('');

  // Get current user info
  const currentUser = getCurrentUser();
  const isAdmin = currentUser?.role === 'admin';
  const userId = currentUser?.id;

  // Fetch cashiers from API
  useEffect(() => {
    const fetchCashiers = async () => {
      setIsLoading(true);
      try {
        if (!userId) return;

        let url = `${config.apiUrl}/cashiers?user_id=${userId}&user_type=${currentUser?.user_type || currentUser?.role}`;

        if (isAdmin && selectedBranchId) {
          // If admin user with selected branch
          url += `&branch_id=${selectedBranchId}`;
        }
        // Branch users don't need branch_id parameter - backend will detect them automatically

        const response = await fetchWithSession(url);
        if (response && response.ok) {
          const data = await response.json();
          // Data is already sorted by server
          setCashiers(data);
        }
      } catch (error) {
        console.error('Error fetching cashiers:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCashiers();
  }, [userId, isAdmin, selectedBranchId]);

  // Fetch branches for admin
  useEffect(() => {
    if (isAdmin) {
      const fetchBranches = async () => {
        try {
          const response = await fetchWithSession(`${config.apiUrl}/branches?user_id=${userId}`);
          if (response && response.ok) {
            const data = await response.json();
            // Data is already sorted by server
            setBranches(data);
          }
        } catch (error) {
          console.error('Error fetching branches:', error);
        }
      };

      fetchBranches();
    }
  }, [isAdmin, userId]);

  // Filter cashiers based on search query
  const filteredCashiers = cashiers.filter(cashier =>
    cashier.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    cashier.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (cashier.branch_name && cashier.branch_name.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Handle delete cashier
  const handleDeleteClick = (cashier: Cashier) => {
    setCashierToDelete(cashier);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    if (!cashierToDelete) return;

    try {
      const response = await fetchWithSession(
        `${config.apiUrl}/cashiers/${cashierToDelete.id}?user_id=${userId}`,
        {
          method: 'DELETE',
        }
      );

      if (response && response.ok) {
        setCashiers(cashiers.filter(c => c.id !== cashierToDelete.id));
        setShowDeleteModal(false);
        setCashierToDelete(null);
      }
    } catch (error) {
      console.error('Error deleting cashier:', error);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Link to="/dashboard-summary" className="p-2 rounded-full hover:bg-neutral-100">
            <ArrowLeft size={20} />
          </Link>
          <h1 className="text-2xl font-bold text-primary-800 ml-2">
            {isAdmin ? "Manajemen Kasir" : "Kasir Cabang"}
          </h1>
        </div>
        <Link
          to="/cashier/new"
          className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg flex items-center"
        >
          <Plus className="h-5 w-5 mr-2" />
          Tambah Kasir
        </Link>
      </div>

      <div className="bg-white rounded-xl shadow-sm overflow-hidden">
        <div className="p-4 border-b border-neutral-200">
          <div className="flex flex-col md:flex-row gap-4 items-center">
            <div className="relative flex-grow w-full">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search size={18} className="text-neutral-400" />
              </div>
              <input
                type="text"
                placeholder="Cari kasir..."
                className="pl-10 p-2 border border-neutral-300 rounded-md w-full focus:outline-none focus:ring-2 focus:ring-primary-500"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                autoComplete="off"
              />
            </div>

            {isAdmin && (
              <select
                className="p-2 border border-neutral-300 rounded-md md:w-64 w-full focus:outline-none focus:ring-2 focus:ring-primary-500 appearance-none"
                value={selectedBranchId}
                onChange={(e) => setSelectedBranchId(e.target.value)}
              >
                <option value="">Semua Cabang</option>
                {branches.map(branch => (
                  <option key={branch.id} value={branch.id}>
                    {branch.name}
                  </option>
                ))}
              </select>
            )}
          </div>
        </div>

        {isLoading ? (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-primary-500 border-t-transparent"></div>
            <p className="mt-2 text-neutral-600">Memuat data kasir...</p>
          </div>
        ) : filteredCashiers.length === 0 ? (
          <div className="text-center py-8 text-neutral-500">
            <p>Tidak ada kasir yang ditemukan</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-neutral-50">
                <tr className="border-b border-neutral-200">
                  <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">Nama</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">Email</th>
                  {isAdmin && (
                    <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">Cabang</th>
                  )}
                  <th className="px-6 py-3 text-right text-xs font-medium text-neutral-500 uppercase tracking-wider">Aksi</th>
                </tr>
              </thead>
              <tbody>
                {filteredCashiers.map((cashier) => (
                  <tr key={cashier.id} className="border-b border-neutral-100 hover:bg-neutral-50">
                    <td className="px-4 py-3 font-medium text-neutral-800">{cashier.name}</td>
                    <td className="px-4 py-3 text-neutral-600">{cashier.email}</td>
                    {isAdmin && (
                      <td className="px-4 py-3 text-neutral-600">
                        {cashier.branch_name || 'Admin (Pusat)'}
                      </td>
                    )}
                    <td className="px-4 py-3">
                      <div className="flex justify-end gap-2">
                        <Link
                          to={`/cashier/edit/${cashier.id}`}
                          className="p-2 text-primary-600 hover:bg-primary-50 rounded-full"
                          aria-label={`Edit ${cashier.name}`}
                        >
                          <Edit size={18} />
                        </Link>
                        <button
                          onClick={() => handleDeleteClick(cashier)}
                          className="p-2 text-red-600 hover:bg-red-50 rounded-full"
                          aria-label={`Hapus ${cashier.name}`}
                        >
                          <Trash2 size={18} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteModal && cashierToDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-medium text-neutral-900 mb-4">Konfirmasi Hapus</h3>
            <p className="text-neutral-600 mb-6">
              Apakah Anda yakin ingin menghapus kasir <span className="font-medium">{cashierToDelete.name}</span>?
              Tindakan ini tidak dapat dibatalkan.
            </p>
            <div className="flex justify-end gap-3">
              <button
                onClick={() => setShowDeleteModal(false)}
                className="px-4 py-2 border border-neutral-300 rounded-md text-neutral-700 hover:bg-neutral-50"
              >
                Batal
              </button>
              <button
                onClick={confirmDelete}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
              >
                Hapus
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CashierManagement;









