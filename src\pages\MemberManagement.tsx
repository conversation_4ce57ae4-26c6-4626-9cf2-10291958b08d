import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { ArrowLeft, Edit, Trash2, Plus, Search } from 'lucide-react';
import config from '../config';
import { fetchWithSession, getCurrentUser } from '../utils/api';

interface Member {
  id: number;
  name: string;
  phone: string;
  email: string | null;
  address: string | null;
  birthdate: string | null;
  is_active: number;
  notes: string | null;
  created_at: string;
  updated_at: string;
}

const MemberManagement: React.FC = () => {
  const [members, setMembers] = useState<Member[]>([]);
  const [filteredMembers, setFilteredMembers] = useState<Member[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [memberToDelete, setMemberToDelete] = useState<Member | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  const navigate = useNavigate();

  // Fetch members from API
  useEffect(() => {
    const fetchMembers = async () => {
      setIsLoading(true);
      try {
        // Dapatkan user_id dari user yang sedang login
        const currentUser = getCurrentUser();
        const userId = currentUser?.id;
        const userRole = currentUser?.role;

        if (!userId) {
          return;
        }

        // Tambahkan user_id dan user_type sebagai query parameter
        let url = `${config.apiUrl}/members?user_id=${userId}&user_type=${currentUser.user_type || userRole}`;

        // Jika user adalah cabang, tambahkan parameter branch_id
        if (userRole === 'branch') {
          url += `&branch_id=${userId}`;
        }

        const response = await fetchWithSession(url);
        if (response && response.ok) {
          const data = await response.json();
          // Data is already sorted by server
          setMembers(data);
        }
      } catch (error) { } finally {
        setIsLoading(false);
      }
    };

    fetchMembers();
  }, []);

  // Filter members based on search query
  useEffect(() => {
    const filtered = members.filter(member =>
      member.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      member.phone.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (member.email && member.email.toLowerCase().includes(searchQuery.toLowerCase()))
    );
    setFilteredMembers(filtered);
  }, [searchQuery, members]);

  const handleDeleteClick = (member: Member) => {
    setMemberToDelete(member);
    setIsDeleteModalOpen(true);
  };

  const closeDeleteModal = () => {
    setIsDeleteModalOpen(false);
    setMemberToDelete(null);
  };

  const confirmDelete = async () => {
    if (memberToDelete) {
      try {
        // Dapatkan user_id dari user yang sedang login
        const currentUser = getCurrentUser();
        const userId = currentUser?.id;

        const response = await fetchWithSession(`${config.apiUrl}/members/${memberToDelete.id}?user_id=${userId}`, {
          method: 'DELETE'
        });

        if (response && response.ok) {
          // Remove member from state
          const updatedMembers = members.filter(m => m.id !== memberToDelete.id);
          setMembers(updatedMembers);
        } else {
          const errorData = await response?.json();
          alert(errorData?.error || 'Gagal menghapus member');
        }
      } catch (error) {
        alert('Terjadi kesalahan saat menghapus member');
      }

      setIsDeleteModalOpen(false);
      setMemberToDelete(null);
    }
  };

  const handleAddMember = () => {
    navigate('/member/new');
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Link to="/dashboard-summary" className="p-2 rounded-full hover:bg-neutral-100">
            <ArrowLeft size={20} />
          </Link>
          <h1 className="text-2xl font-bold text-primary-800 ml-2">Manajemen Member</h1>
        </div>
        <button
          onClick={handleAddMember}
          className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg flex items-center"
        >
          <Plus className="h-5 w-5 mr-2" />
          Tambah Member
        </button>
      </div>

      <div className="bg-white rounded-xl shadow-sm overflow-hidden">
        <div className="p-4 border-b border-neutral-200">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search size={18} className="text-neutral-400" />
            </div>
            <input
              type="text"
              placeholder="Cari member..."
              className="w-full pl-10 pr-4 py-2 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              autoComplete="off"
            />
          </div>
        </div>

        {isLoading ? (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-primary-500 border-t-transparent"></div>
            <p className="mt-2 text-neutral-600">Memuat data member...</p>
          </div>
        ) : filteredMembers.length === 0 ? (
          <div className="text-center py-8 text-neutral-500">
            <p>Tidak ada member yang ditemukan</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-neutral-50">
                <tr className="border-b border-neutral-200">
                  <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">Nama</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">Telepon</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">Email</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-neutral-500 uppercase tracking-wider">Aksi</th>
                </tr>
              </thead>
              <tbody>
                {filteredMembers.map((member) => (
                  <tr key={member.id} className="border-b border-neutral-100 hover:bg-neutral-50">
                    <td className="px-4 py-3 font-medium text-neutral-800">{member.name}</td>
                    <td className="px-4 py-3 text-neutral-600">{member.phone}</td>
                    <td className="px-4 py-3 text-neutral-600">{member.email || '-'}</td>
                    <td className="px-4 py-3">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${member.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                        {member.is_active ? 'Aktif' : 'Tidak Aktif'}
                      </span>
                    </td>
                    <td className="px-4 py-3">
                      <div className="flex justify-end gap-2">
                        <Link
                          to={`/member/edit/${member.id}`}
                          className="p-2 text-primary-600 hover:bg-primary-50 rounded-full"
                          aria-label={`Edit ${member.name}`}
                        >
                          <Edit size={18} />
                        </Link>
                        <button
                          onClick={() => handleDeleteClick(member)}
                          className="p-2 text-red-600 hover:bg-red-50 rounded-full"
                          aria-label={`Hapus ${member.name}`}
                        >
                          <Trash2 size={18} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-medium text-neutral-900 mb-4">Konfirmasi Hapus</h3>
            <p className="text-neutral-600 mb-6">
              Apakah Anda yakin ingin menghapus member <span className="font-semibold">{memberToDelete?.name}</span>?
              Tindakan ini tidak dapat dibatalkan.
            </p>
            <div className="flex justify-end gap-3">
              <button
                onClick={closeDeleteModal}
                className="px-4 py-2 border border-neutral-300 rounded-lg text-neutral-700 hover:bg-neutral-50"
              >
                Batal
              </button>
              <button
                onClick={confirmDelete}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
              >
                Hapus
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MemberManagement;






