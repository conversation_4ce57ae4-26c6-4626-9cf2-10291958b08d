import React, { useState, useEffect } from 'react';
import { ArrowLeft, Plus, Search, Edit, Trash2, AlertTriangle } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import { Product, Category } from '../types';
import config from '../config';
import { fetchWithSession, getCurrentUser, buildApiUrl } from '../utils/api';

const ProductManagement: React.FC = () => {
  const navigate = useNavigate();
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [productToDelete, setProductToDelete] = useState<Product | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isCategoriesLoading, setIsCategoriesLoading] = useState(true);

  // Get current user info
  const currentUser = getCurrentUser();
  const isBranchUser = currentUser?.role === 'branch';

  useEffect(() => {
    // Fetch products from API
    const fetchProducts = async () => {
      setIsLoading(true);
      try {
        // Dapatkan user_id dari user yang sedang login
        const currentUser = getCurrentUser();
        const userId = currentUser?.id;

        // Build URL dengan parameter yang benar untuk kasir
        const apiUrl = buildApiUrl(`${config.apiUrl}/products`);
        const response = await fetchWithSession(apiUrl);
        if (response && response.ok) {
          const data = await response.json();
          // Transform data to match our interface
          const formattedProducts = data.map((product: any) => ({
            id: product.id.toString(),
            name: product.name,
            price: product.price,
            costPrice: product.cost_price || 0,
            image: product.image,
            category: product.category,
            categoryLabel: product.category_label,
            userId: product.user_id,
            branchId: product.branch_id
          }));
          // Data is already sorted by server
          setProducts(formattedProducts);
        }
      } catch (error) { } finally {
        setIsLoading(false);
      }
    };

    fetchProducts();
  }, []);

  useEffect(() => {
    // Fetch categories
    const fetchCategories = async () => {
      setIsCategoriesLoading(true);
      try {
        const currentUser = getCurrentUser();
        const userId = currentUser?.id;

        const response = await fetchWithSession(`${config.apiUrl}/categories?user_id=${userId}`);
        if (response && response.ok) {
          const data = await response.json();
          // Filter out the "all" category
          const filteredCategories = data
            .filter((cat: any) => cat.value !== 'all')
            .map((cat: any) => ({
              id: cat.id.toString(),
              value: cat.value,
              label: cat.label,
              icon: cat.icon,
              count: cat.count || 0
            }));
          setCategories(filteredCategories);
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
      } finally {
        setIsCategoriesLoading(false);
      }
    };

    fetchCategories();
  }, []);

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    product.categoryLabel.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleDeleteClick = (product: Product) => {
    setProductToDelete(product);
    setIsDeleteModalOpen(true);
  };

  const confirmDelete = async () => {
    if (productToDelete) {
      try {
        // Dapatkan user_id dari user yang sedang login
        const currentUser = getCurrentUser();
        const userId = currentUser?.id;
        const userType = currentUser?.user_type || (currentUser?.role === 'branch' ? 'branch' : currentUser?.role === 'cashier' ? 'cashier' : 'msuser');

        const response = await fetchWithSession(`${config.apiUrl}/products/${productToDelete.id}?user_id=${userId}&user_type=${userType}`, {
          method: 'DELETE'
        });

        if (response && response.ok) {
          // Remove product from state
          const updatedProducts = products.filter(p => p.id !== productToDelete.id);
          setProducts(updatedProducts);
        } else {
          const errorData = await response?.json();
          alert(errorData?.error || 'Gagal menghapus produk');
        }
      } catch (error) {
        alert('Terjadi kesalahan saat menghapus produk');
      }

      setIsDeleteModalOpen(false);
      setProductToDelete(null);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(price);
  };

  const handleAddProduct = () => {
    // Periksa apakah ada kategori selain "all"
    if (categories.length === 0) {
      // Tampilkan alert jika tidak ada kategori
      if (window.confirm('Anda belum memiliki kategori produk. Tambahkan kategori terlebih dahulu sebelum menambahkan produk?')) {
        navigate('/categories');
      }
      return;
    }

    // Jika ada kategori, lanjutkan ke halaman tambah produk
    navigate('/product/new');
  };

  // Fungsi untuk mendapatkan URL gambar yang benar
  const getImageUrl = (imagePath: string) => {
    if (!imagePath) {
      return 'https://via.placeholder.com/150?text=No+Image';
    }

    if (imagePath.startsWith('/uploads/')) {
      return `${config.imageBaseUrl}${imagePath}`;
    }

    // For URL images, return as-is
    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
      return imagePath;
    }

    // For relative paths, prepend imageBaseUrl
    return `${config.imageBaseUrl}${imagePath}`;
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 gap-3">
        <div className="flex items-center gap-3">
          <Link to="/dashboard-summary" className="p-2 rounded-full hover:bg-neutral-100">
            <ArrowLeft size={20} />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-primary-800">Manajemen Produk</h1>
            {isBranchUser && (
              <p className="text-sm text-neutral-600 mt-1">
                Mengelola produk untuk cabang: <span className="font-medium">{currentUser?.name}</span>
              </p>
            )}
          </div>
        </div>
        <button
          onClick={handleAddProduct}
          className="flex items-center justify-center gap-2 bg-primary-600 text-white py-2 px-4 rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 w-full sm:w-auto"
        >
          <Plus size={18} />
          <span>Tambah Produk</span>
        </button>
      </div>

      <div className="bg-white rounded-xl shadow-sm">
        {categories.length === 0 && !isCategoriesLoading && (
          <div className="p-4 bg-amber-50 border-l-4 border-amber-500 text-amber-700 flex items-start gap-3">
            <AlertTriangle size={20} className="mt-0.5 flex-shrink-0" />
            <div>
              <p className="font-medium">Anda belum memiliki kategori produk</p>
              <p className="text-sm mt-1">Tambahkan kategori terlebih dahulu sebelum menambahkan produk.</p>
              <Link
                to="/categories"
                className="inline-block mt-2 text-sm font-medium text-amber-700 hover:text-amber-800 underline"
              >
                Ke Halaman Kategori
              </Link>
            </div>
          </div>
        )}

        <div className="p-4 border-b">
          <div className="relative">
            <input
              type="text"
              placeholder="Cari produk..."
              className="w-full pl-10 pr-4 py-2 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              autoComplete="off"
            />
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400">
              <Search size={18} />
            </div>
          </div>
        </div>

        {isLoading ? (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-primary-500 border-t-transparent"></div>
            <p className="mt-2 text-neutral-600">Memuat data produk...</p>
          </div>
        ) : filteredProducts.length === 0 ? (
          <div className="text-center py-8 text-neutral-500">
            <p>Tidak ada produk yang ditemukan</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-neutral-50">
                <tr className="border-b border-neutral-200">
                  <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">Gambar</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">Nama Produk</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">Kategori</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">Sumber</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">Harga Modal</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">Harga Jual</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-neutral-500 uppercase tracking-wider">Aksi</th>
                </tr>
              </thead>
              <tbody>
                {filteredProducts.map((product) => (
                  <tr key={product.id} className="border-b border-neutral-100 hover:bg-neutral-50">
                    <td className="px-4 py-3">
                      <div className="h-12 w-12 rounded-md overflow-hidden">
                        <img
                          src={getImageUrl(product.image)}
                          alt={product.name}
                          className="h-full w-full object-cover"
                          onError={(e) => {
                            e.currentTarget.src = 'https://via.placeholder.com/150?text=Error';
                          }}
                        />
                      </div>
                    </td>
                    <td className="px-4 py-3 font-medium text-neutral-800">{product.name}</td>
                    <td className="px-4 py-3">
                      <span className="px-2 py-1 bg-neutral-100 text-neutral-700 rounded-full text-xs">
                        {product.categoryLabel}
                      </span>
                    </td>
                    <td className="px-4 py-3">
                      <span className={`px-2 py-1 rounded-full text-xs ${product.branchId
                        ? 'bg-blue-100 text-blue-700'
                        : 'bg-green-100 text-green-700'
                        }`}>
                        {product.branchId ? (isBranchUser ? 'Produk Cabang' : 'Cabang') : 'Admin'}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-neutral-700">{formatPrice(product.costPrice || 0)}</td>
                    <td className="px-4 py-3 text-neutral-700">{formatPrice(product.price)}</td>
                    <td className="px-4 py-3">
                      <div className="flex justify-end gap-2">
                        <Link
                          to={`/product/edit/${product.id}`}
                          className="p-2 text-primary-600 hover:bg-primary-50 rounded-full"
                          aria-label={`Edit ${product.name}`}
                        >
                          <Edit size={18} />
                        </Link>
                        <button
                          onClick={() => handleDeleteClick(product)}
                          className="p-2 text-red-600 hover:bg-red-50 rounded-full"
                          aria-label={`Hapus ${product.name}`}
                        >
                          <Trash2 size={18} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-medium text-neutral-900 mb-4">Konfirmasi Hapus</h3>
            <p className="text-neutral-600 mb-6">
              Apakah Anda yakin ingin menghapus produk "{productToDelete?.name}"? Tindakan ini tidak dapat dibatalkan.
            </p>
            <div className="flex justify-end gap-3">
              <button
                onClick={() => setIsDeleteModalOpen(false)}
                className="px-4 py-2 border border-neutral-300 rounded-md text-neutral-700 hover:bg-neutral-50"
              >
                Batal
              </button>
              <button
                onClick={confirmDelete}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
              >
                Hapus
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductManagement;










