const express = require('express');
const router = express.Router();
const pool = require('../config/database');
const { getUserRole } = require('../utils/userRoleHelper');

// Get all expenses with filtering
router.get('/', async (req, res) => {
  try {
    const { user_id, branch_id, start_date, end_date, category, user_type } = req.query;

    if (!user_id) {
      return res.status(400).json({ error: 'user_id is required' });
    }

    // Get user role to determine access level
    const userRole = await getUserRole(user_id, user_type);
    if (!userRole) {
      return res.status(404).json({ error: 'User not found' });
    }
    let whereConditions = [];
    let queryParams = [];

    // Access control based on user role
    if (userRole === 'admin') {
      // Admin can see all expenses, optionally filtered by branch
      if (branch_id) {
        // When filtering by specific branch, only show expenses from that branch
        // Verify admin has access to this branch first
        const [branchAccess] = await pool.query(
          'SELECT id FROM branches WHERE id = ? AND user_id = ?',
          [branch_id, user_id]
        );

        if (branchAccess.length === 0) {
          return res.status(403).json({ error: 'Access denied to this branch' });
        }

        whereConditions.push('e.branch_id = ?');
        queryParams.push(branch_id);
      } else {
        // Show all expenses from admin's branches and direct admin expenses
        whereConditions.push(`(
          e.user_id = ? OR
          e.branch_id IN (SELECT id FROM branches WHERE user_id = ?) OR
          e.user_id IN (SELECT id FROM branches WHERE user_id = ?)
        )`);
        queryParams.push(user_id, user_id, user_id);
      }
    } else if (userRole === 'branch') {
      // Branch users can only see expenses from their branch
      whereConditions.push('e.branch_id = ?');
      queryParams.push(user_id);
    } else {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Date filtering
    if (start_date) {
      whereConditions.push('DATE(e.date) >= ?');
      queryParams.push(start_date);
    }
    if (end_date) {
      whereConditions.push('DATE(e.date) <= ?');
      queryParams.push(end_date);
    }

    // Category filtering
    if (category) {
      whereConditions.push('e.category = ?');
      queryParams.push(category);
    }

    const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';

    const query = `
      SELECT 
        e.*,
        u.name as user_name,
        b.name as branch_name
      FROM expenses e
      LEFT JOIN msusers u ON e.user_id = u.id
      LEFT JOIN branches b ON e.branch_id = b.id
      ${whereClause}
      ORDER BY e.date DESC, e.created_at DESC
    `;

    const [expenses] = await pool.query(query, queryParams);
    res.json(expenses);
  } catch (error) {
    console.error('Error fetching expenses:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get expense by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { user_id, user_type } = req.query;

    if (!user_id) {
      return res.status(400).json({ error: 'user_id is required' });
    }

    // Get user role to determine access level
    const userRole = await getUserRole(user_id, user_type);
    if (!userRole) {
      return res.status(404).json({ error: 'User not found' });
    }

    let query = `
      SELECT 
        e.*,
        u.name as user_name,
        b.name as branch_name
      FROM expenses e
      LEFT JOIN msusers u ON e.user_id = u.id
      LEFT JOIN branches b ON e.branch_id = b.id
      WHERE e.id = ?
    `;

    // Access control
    if (userRole === 'branch') {
      query += ' AND e.branch_id = ?';
      const [expense] = await pool.query(query, [id, user_id]);
      if (expense.length === 0) {
        return res.status(404).json({ error: 'Expense not found' });
      }
      res.json(expense[0]);
    } else if (userRole === 'admin') {
      const [expense] = await pool.query(query, [id]);
      if (expense.length === 0) {
        return res.status(404).json({ error: 'Expense not found' });
      }
      res.json(expense[0]);
    } else {
      return res.status(403).json({ error: 'Access denied' });
    }
  } catch (error) {
    console.error('Error fetching expense:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Create new expense
router.post('/', async (req, res) => {
  try {
    const { description, amount, category, date, branch_id, user_id, user_type } = req.body;

    if (!description || !amount || !date || !user_id) {
      return res.status(400).json({ error: 'description, amount, date, and user_id are required' });
    }

    // Get user role to determine access level
    const userRole = await getUserRole(user_id, user_type);
    if (!userRole) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Determine the correct branch_id and user_id for the expense
    let finalBranchId = branch_id;
    let finalUserId = user_id;

    if (userRole === 'branch') {
      // For branch users, they can only create expenses for their own branch
      // The user_id is actually their branch_id, so set branch_id = user_id
      // and user_id should be the admin who owns this branch
      finalBranchId = user_id; // Set branch_id to the branch ID

      // Get the admin user_id for this branch
      const [branchInfo] = await pool.query(
        'SELECT user_id FROM branches WHERE id = ?',
        [user_id]
      );

      if (branchInfo.length > 0) {
        finalUserId = branchInfo[0].user_id; // Set user_id to the admin who owns this branch
      }
    } else if (userRole === 'admin') {
      // For admin users, validate branch access if branch_id is specified
      if (branch_id) {
        const [branchAccess] = await pool.query(
          'SELECT id FROM branches WHERE id = ? AND user_id = ?',
          [branch_id, user_id]
        );

        if (branchAccess.length === 0) {
          return res.status(403).json({ error: 'Access denied to this branch' });
        }
      }
    }

    const query = `
      INSERT INTO expenses (description, amount, category, date, branch_id, user_id)
      VALUES (?, ?, ?, ?, ?, ?)
    `;

    const [result] = await pool.query(query, [description, amount, category, date, finalBranchId, finalUserId]);

    // Get the created expense
    const [newExpense] = await pool.query(`
      SELECT 
        e.*,
        u.name as user_name,
        b.name as branch_name
      FROM expenses e
      LEFT JOIN msusers u ON e.user_id = u.id
      LEFT JOIN branches b ON e.branch_id = b.id
      WHERE e.id = ?
    `, [result.insertId]);

    res.status(201).json(newExpense[0]);
  } catch (error) {
    console.error('Error creating expense:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update expense
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { description, amount, category, date, branch_id, user_id, user_type } = req.body;

    if (!user_id) {
      return res.status(400).json({ error: 'user_id is required' });
    }

    // Get user role to determine access level
    const userRole = await getUserRole(user_id, user_type);
    if (!userRole) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Check if expense exists and user has access
    let accessQuery = 'SELECT * FROM expenses WHERE id = ?';
    let accessParams = [id];

    if (userRole === 'branch') {
      accessQuery += ' AND branch_id = ?';
      accessParams.push(user_id);
    }

    const [existingExpense] = await pool.query(accessQuery, accessParams);
    if (existingExpense.length === 0) {
      return res.status(404).json({ error: 'Expense not found or access denied' });
    }

    // Validate branch access for branch users
    if (userRole === 'branch' && branch_id) {
      const [branchResult] = await pool.query('SELECT id FROM branches WHERE id = ? AND user_id = ?', [branch_id, user_id]);
      if (branchResult.length === 0) {
        return res.status(403).json({ error: 'Access denied to this branch' });
      }
    }

    const query = `
      UPDATE expenses 
      SET description = ?, amount = ?, category = ?, date = ?, branch_id = ?
      WHERE id = ?
    `;

    await pool.query(query, [description, amount, category, date, branch_id, id]);

    // Get the updated expense
    const [updatedExpense] = await pool.query(`
      SELECT 
        e.*,
        u.name as user_name,
        b.name as branch_name
      FROM expenses e
      LEFT JOIN msusers u ON e.user_id = u.id
      LEFT JOIN branches b ON e.branch_id = b.id
      WHERE e.id = ?
    `, [id]);

    res.json(updatedExpense[0]);
  } catch (error) {
    console.error('Error updating expense:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Delete expense
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { user_id } = req.query;

    if (!user_id) {
      return res.status(400).json({ error: 'user_id is required' });
    }

    // Get user role to determine access level
    const userRole = await getUserRole(user_id, req.query.user_type);
    if (!userRole) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Check if expense exists and user has access
    let accessQuery = 'SELECT * FROM expenses WHERE id = ?';
    let accessParams = [id];

    if (userRole === 'branch') {
      accessQuery += ' AND branch_id = ?';
      accessParams.push(user_id);
    }

    const [existingExpense] = await pool.query(accessQuery, accessParams);
    if (existingExpense.length === 0) {
      return res.status(404).json({ error: 'Expense not found or access denied' });
    }

    await pool.query('DELETE FROM expenses WHERE id = ?', [id]);
    res.json({ message: 'Expense deleted successfully' });
  } catch (error) {
    console.error('Error deleting expense:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get expense categories
router.get('/categories/list', async (req, res) => {
  try {
    const { user_id } = req.query;

    if (!user_id) {
      return res.status(400).json({ error: 'user_id is required' });
    }

    // Get user role to determine access level
    const userRole = await getUserRole(user_id, req.query.user_type);
    if (!userRole) {
      return res.status(404).json({ error: 'User not found' });
    }
    let whereCondition = '';
    let queryParams = [];

    // Access control based on user role
    if (userRole === 'admin') {
      whereCondition = `WHERE (
        user_id = ? OR 
        branch_id IN (SELECT id FROM branches WHERE user_id = ?) OR
        user_id IN (SELECT id FROM branches WHERE user_id = ?)
      )`;
      queryParams = [user_id, user_id, user_id];
    } else if (userRole === 'branch') {
      whereCondition = 'WHERE user_id = ?';
      queryParams = [user_id];
    } else {
      return res.status(403).json({ error: 'Access denied' });
    }

    const query = `
      SELECT DISTINCT category
      FROM expenses
      ${whereCondition}
      AND category IS NOT NULL
      ORDER BY category ASC
    `;

    const [categories] = await pool.query(query, queryParams);
    res.json(categories.map(row => row.category));
  } catch (error) {
    console.error('Error fetching expense categories:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
